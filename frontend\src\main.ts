// 🔧 修复：移除冗余的Element Plus全局导入，使用Vite插件按需导入
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import { createApp, nextTick } from 'vue'
import './assets/main.css'

import App from './App.vue'
import router from './router'

// 🔥 修复：设置全局router实例以支持统一导航
import { setGlobalRouter } from '@/utils/navigation'

const app = createApp(App)
const pinia = createPinia()

// 配置Pinia持久化插件
pinia.use(piniaPluginPersistedstate)

// 🔧 修复：移除全局图标注册，使用按需导入

app.use(pinia)
app.use(router)
// 🔧 修复：移除ElementPlus全局注册，使用Vite插件按需导入

// 🔥 修复：设置全局router实例以支持统一导航
setGlobalRouter(router)

// 挂载应用
app.mount('#app')

// 🔧 遵循 Pinia 最佳实践：应用启动后异步初始化 stores
// 使用 nextTick 确保应用完全初始化后再使用 store

nextTick(() => {
  // 🔧 优化：简化应用启动逻辑，认证检查统一由路由守卫处理
  console.log('[App] initialized and ready')
})
