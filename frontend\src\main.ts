// 🔧 修复：移除冗余的Element Plus全局导入，使用Vite插件按需导入
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import { createApp, nextTick } from 'vue'
import './assets/main.css'

import App from './App.vue'
import router from './router'

// 🔥 修复：设置全局router实例以支持统一导航
import { setGlobalRouter } from '@/utils/navigation'

const app = createApp(App)
const pinia = createPinia()

// 配置Pinia持久化插件
pinia.use(piniaPluginPersistedstate)

// 🔧 修复：移除全局图标注册，使用按需导入

app.use(pinia)
app.use(router)
// 🔧 修复：移除ElementPlus全局注册，使用Vite插件按需导入

// 🔥 修复：设置全局router实例以支持统一导航
setGlobalRouter(router)

// 挂载应用
app.mount('#app')

// 🔧 遵循 Pinia 最佳实践：应用启动后异步初始化 stores
// 使用 nextTick 确保应用完全初始化后再使用 store

nextTick(() => {
  // 动态导入并初始化 stores，确保 Pinia 已完全注册
  import('@/stores/session')
    .then(async (sessionModule) => {
      const sessionStore = sessionModule.useSessionStore()

      // 🔧 修复：移除启动时清空历史记录，改为在登出时清空

      // 🔥 修改：防止在登录页面触发会话检查
      if (window.location.pathname !== '/login') {
        console.log('[App] 正在初始化会话状态...')
        try {
          await sessionStore.initializeSession()
          console.log('[App] 会话状态初始化完成')
        } catch (error) {
          console.warn('[App] 会话状态初始化失败:', error)
        }

        // 延迟启动会话监控（只在非登录页面）
        setTimeout(() => {
          console.log('[App] 启动会话监控（跳过初始检查）')
          sessionStore.startSessionMonitoring()
        }, 5000)
      } else {
        console.log('[App] 在登录页面，跳过会话初始化和监控')
      }

      console.log('[App] 所有 stores 初始化完成')
    })
    .catch((error) => {
      console.error('[App] Stores 初始化失败:', error)
    })
})
