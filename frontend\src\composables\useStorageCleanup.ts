import { useNotificationStore } from '@/stores/notification'

// 🔥 存储清理相关常量
const FILES_DB_NAME = 'visa_form_files'
const FILES_STORE_NAME = 'uploaded_files'
const CLEANUP_STORAGE_KEY = 'storage_cleanup_config'
const LAST_CLEANUP_KEY = 'last_cleanup_timestamp'

// 默认清理配置
const DEFAULT_CLEANUP_CONFIG = {
  // 文件过期时间（7天）
  fileExpirationDays: 7,
  // 自动清理间隔（24小时）
  autoCleanupIntervalHours: 24,
  // 最大存储容量（50MB）
  maxStorageSize: 50 * 1024 * 1024,
  // 清理警告阈值（40MB）
  warningThreshold: 40 * 1024 * 1024,
  // 是否启用自动清理
  autoCleanupEnabled: true,
}

interface CleanupConfig {
  fileExpirationDays: number
  autoCleanupIntervalHours: number
  maxStorageSize: number
  warningThreshold: number
  autoCleanupEnabled: boolean
}

interface StorageStats {
  totalFiles: number
  totalSize: number
  expiredFiles: number
  oldestFileDate: Date | null
  newestFileDate: Date | null
}

interface FileData {
  id: string
  timestamp: number
  file?: {
    size: number
  }
}

export const useStorageCleanup = () => {
  // 初始化通知store
  const notificationStore = useNotificationStore()

  // 获取清理配置
  const getCleanupConfig = (): CleanupConfig => {
    try {
      const stored = localStorage.getItem(CLEANUP_STORAGE_KEY)
      if (stored) {
        return { ...DEFAULT_CLEANUP_CONFIG, ...JSON.parse(stored) }
      }
    } catch (error) {
      console.warn('⚠️ 无法加载清理配置，使用默认配置:', error)
    }
    return DEFAULT_CLEANUP_CONFIG
  }

  // 保存清理配置
  const saveCleanupConfig = (config: Partial<CleanupConfig>) => {
    try {
      const currentConfig = getCleanupConfig()
      const newConfig = { ...currentConfig, ...config }
      localStorage.setItem(CLEANUP_STORAGE_KEY, JSON.stringify(newConfig))
      // 🔧 修复：移除调试日志
    } catch (error) {
      console.error('❌ 保存清理配置失败:', error)
    }
  }

  // 打开IndexedDB
  const openFilesDB = (): Promise<IDBDatabase> => {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(FILES_DB_NAME)
      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve(request.result)
    })
  }

  // 获取存储统计信息
  const getStorageStats = async (): Promise<StorageStats> => {
    try {
      const db = await openFilesDB()
      const transaction = db.transaction([FILES_STORE_NAME], 'readonly')
      const store = transaction.objectStore(FILES_STORE_NAME)

      const allFiles = await new Promise<FileData[]>((resolve, reject) => {
        const request = store.getAll()
        request.onsuccess = () => resolve(request.result || [])
        request.onerror = () => reject(request.error)
      })

      const config = getCleanupConfig()
      const now = Date.now()
      const expirationTime = config.fileExpirationDays * 24 * 60 * 60 * 1000

      let totalSize = 0
      let expiredFiles = 0
      let oldestFileDate: Date | null = null
      let newestFileDate: Date | null = null

      allFiles.forEach((fileData) => {
        const fileSize = fileData.file?.size ?? 0
        totalSize += fileSize

        const fileDate = new Date(fileData.timestamp)
        if (!oldestFileDate || fileDate < oldestFileDate) {
          oldestFileDate = fileDate
        }
        if (!newestFileDate || fileDate > newestFileDate) {
          newestFileDate = fileDate
        }

        // 检查是否过期
        if (now - fileData.timestamp > expirationTime) {
          expiredFiles++
        }
      })

      return {
        totalFiles: allFiles.length,
        totalSize,
        expiredFiles,
        oldestFileDate,
        newestFileDate,
      }
    } catch (error) {
      console.warn('⚠️ 无法获取存储统计信息:', error)
      return {
        totalFiles: 0,
        totalSize: 0,
        expiredFiles: 0,
        oldestFileDate: null,
        newestFileDate: null,
      }
    }
  }

  // 清理过期文件
  const cleanupExpiredFiles = async (): Promise<number> => {
    try {
      const config = getCleanupConfig()
      const db = await openFilesDB()
      const transaction = db.transaction([FILES_STORE_NAME], 'readwrite')
      const store = transaction.objectStore(FILES_STORE_NAME)

      const allFiles = await new Promise<FileData[]>((resolve, reject) => {
        const request = store.getAll()
        request.onsuccess = () => resolve(request.result || [])
        request.onerror = () => reject(request.error)
      })

      const now = Date.now()
      const expirationTime = config.fileExpirationDays * 24 * 60 * 60 * 1000
      let cleanedCount = 0

      for (const fileData of allFiles) {
        if (now - fileData.timestamp > expirationTime) {
          await new Promise<void>((resolve, reject) => {
            const deleteRequest = store.delete(fileData.id)
            deleteRequest.onsuccess = () => {
              cleanedCount++
              resolve()
            }
            deleteRequest.onerror = () => reject(deleteRequest.error)
          })
        }
      }

      // 🔧 修复：移除调试日志
      return cleanedCount
    } catch (error) {
      console.error('❌ 清理过期文件失败:', error)
      return 0
    }
  }

  // 完整清理操作
  const performFullCleanup = async (): Promise<{
    expired: number
    oversized: number
    smart: number
  }> => {
    const stats = await getStorageStats()

    // 如需在生产环境记录存储状态，请使用合适的日志系统
    // console.log(
    //   `📊 当前存储状态: ${stats.totalFiles} 个文件，${(stats.totalSize / 1024 / 1024).toFixed(2)}MB`,
    // )

    // 1. 清理过期文件
    const expiredCount = await cleanupExpiredFiles()

    // 更新最后清理时间
    localStorage.setItem(LAST_CLEANUP_KEY, Date.now().toString())

    const result = { expired: expiredCount, oversized: 0, smart: 0 }
    // 🔧 修复：移除调试日志

    return result
  }

  // 初始化自动清理
  const initAutoCleanup = async () => {
    const config = getCleanupConfig()

    // 检查当前存储状态
    const stats = await getStorageStats()

    // 如果超过警告阈值，显示警告
    if (stats.totalSize > config.warningThreshold) {
      notificationStore.showWarning(
        `本地存储已使用 ${(stats.totalSize / 1024 / 1024).toFixed(2)}MB，建议清理过期文件`,
      )
    }

    // 如果超过最大容量，强制清理
    if (stats.totalSize > config.maxStorageSize) {
      notificationStore.showError('存储空间不足，正在自动清理过期文件...')
      await performFullCleanup()
    }
  }

  // 手动清理
  const manualCleanup = async (): Promise<void> => {
    try {
      const stats = await getStorageStats()

      if (stats.totalFiles === 0) {
        notificationStore.showInfo('没有需要清理的文件')
        return
      }

      const result = await performFullCleanup()
      const totalCleaned = result.expired + result.oversized + result.smart

      if (totalCleaned > 0) {
        notificationStore.showSuccess(`清理完成：删除了 ${totalCleaned} 个文件`)
      } else {
        notificationStore.showInfo('没有找到需要清理的文件')
      }
    } catch (error) {
      console.error('❌ 手动清理失败:', error)
      notificationStore.showError('清理操作失败')
    }
  }

  return {
    // 配置管理
    getCleanupConfig,
    saveCleanupConfig,

    // 统计信息
    getStorageStats,

    // 清理操作
    cleanupExpiredFiles,
    performFullCleanup,
    manualCleanup,

    // 自动清理
    initAutoCleanup,
  }
}
