import { visa<PERSON>pi } from '@/api/visa'
import type { OCRPassportData, OCRResult } from '@/types/form'
import { ElMessage } from 'element-plus'
import { readonly, ref } from 'vue'

// OCR错误类型定义
interface OCRError {
  name?: string
  message?: string
  code?: string
  status?: number
}

/**
 * OCR识别组合式函数 - 严格按照旧版OCR算法和业务逻辑迁移
 * 增强功能：支持取消、错误重试、新旧版本数据结构兼容
 */
export function useOCR() {
  // OCR状态管理 - 对应旧版状态
  const isProcessing = ref(false)
  const currentController = ref<AbortController | null>(null)
  const processingFile = ref<string>('')
  const canCancel = ref(false)

  /**
   * 执行护照OCR识别 - 严格对应旧版 processOCR 函数逻辑
   * 增强功能：支持abort信号、错误重试、状态指示
   * @param file 护照扫描件文件
   * @returns OCR识别结果
   */
  const recognizePassport = async (file: File): Promise<OCRResult> => {
    // 🔧 修复：移除调试日志

    // 文件基础验证
    if (!file || file.size === 0) {
      ElMessage.error('请选择有效的护照图片文件')
      return { success: false, message: '无效的文件' }
    }

    if (file.size > 10 * 1024 * 1024) {
      // 10MB限制
      ElMessage.error('文件大小超过10MB限制')
      return { success: false, message: '文件过大' }
    }

    // 设置处理状态
    isProcessing.value = true
    processingFile.value = file.name
    canCancel.value = true

    // 支持取消请求 - 对应旧版 AbortController 逻辑
    const controller = new AbortController()
    currentController.value = controller

    try {
      // 发送OCR请求

      // 使用统一的API调用，包含超时和取消支持
      const response = await visaApi.ocrPassport(file, controller.signal)

      // 详细的响应调试日志
      // 🔧 修复：移除详细调试日志

      // 检查响应格式：如果是ApiResponse格式，使用data字段；否则直接使用响应
      let ocrData: Record<string, unknown>

      if (response && typeof response === 'object' && 'success' in response && 'data' in response) {
        // 标准ApiResponse格式
        const apiResponse = response as {
          success: boolean
          data?: Record<string, unknown>
          message?: string
        }
        if (apiResponse.success && apiResponse.data) {
          console.log('🔍 标准ApiResponse格式，OCR数据字段:', Object.keys(apiResponse.data))
          console.log('🔍 OCR完整数据:', JSON.stringify(apiResponse.data, null, 2))
          ocrData = apiResponse.data
        } else {
          console.warn('❌ OCR API返回失败状态:', response)
          return {
            success: false,
            data: undefined,
            message: apiResponse.message ?? 'OCR识别失败',
          }
        }
      } else if (response && typeof response === 'object') {
        // 直接OCR数据格式
        console.log('🔍 直接OCR数据格式，字段:', Object.keys(response))
        console.log('🔍 OCR完整数据:', JSON.stringify(response, null, 2))
        ocrData = response as unknown as Record<string, unknown>
      } else {
        console.warn('❌ 无效的OCR响应格式:', response)
        return {
          success: false,
          data: undefined,
          message: 'OCR响应格式无效',
        }
      }

      // 格式化OCR数据为表单字段
      const formattedData = formatOCRDataToFormFields(ocrData)

      // 检查是否有有效数据
      const hasValidData = Object.values(formattedData).some(
        (value) => value && typeof value === 'string' && value.trim() !== '',
      )

      if (hasValidData) {
        return {
          success: true,
          data: formattedData,
          message: '护照信息识别成功',
        }
      } else {
        console.warn('⚠️ OCR识别结果为空')
        return {
          success: false,
          data: undefined,
          message: '未能从护照图片中识别到有效信息，请检查图片质量或手动填写表单',
        }
      }
    } catch (error: unknown) {
      const ocrError = error as OCRError

      // 取消请求处理 - 对应旧版取消逻辑
      if (ocrError.name === 'AbortError') {
        console.log('🚫 OCR请求已取消')
        return {
          success: false,
          message: 'OCR识别已取消',
        }
      }

      console.error('💥 OCR处理失败:', error)

      // 根据错误类型提供不同的用户提示
      let errorMessage = 'OCR识别失败，请重试'

      if (ocrError.code === 'NETWORK_ERROR' || ocrError.message?.includes('fetch')) {
        errorMessage = '网络连接失败，请检查网络后重试'
      } else if (ocrError.code === 'TIMEOUT') {
        errorMessage = 'OCR处理超时，请重试或手动填写'
      } else if (ocrError.status === 400) {
        errorMessage = '图片格式不正确，请上传清晰的护照扫描件'
      } else if (ocrError.status === 413) {
        errorMessage = '文件过大，请压缩后重试'
      } else if (ocrError.status && ocrError.status >= 500) {
        errorMessage = '服务暂时不可用，请稍后重试'
      }

      return {
        success: false,
        message: errorMessage,
      }
    } finally {
      isProcessing.value = false
      processingFile.value = ''
      canCancel.value = false
      currentController.value = null
    }
  }

  /**
   * 格式化OCR数据为表单字段 - 直接使用API返回字段，严格对应后端格式
   * API已经返回了正确的字段格式，不需要复杂的处理逻辑
   */
  const formatOCRDataToFormFields = (
    ocrData: Record<string, unknown> | undefined,
  ): OCRPassportData => {
    try {
      console.log('🔧 开始格式化OCR数据:', ocrData)

      if (!ocrData) {
        console.warn('⚠️ OCR数据为空，返回空对象')
        return {
          surname: '',
          given_name: '',
          chinese_name: '',
          date_of_birth: '',
          place_of_birth: '',
          nationality: '',
          sex: undefined,
          passport_number: '',
          date_of_issue: '',
          place_of_issue: '',
          date_of_expiry: '',
        }
      }

      // 直接使用API返回的字段 - 后端已经处理好所有格式
      const formattedData: OCRPassportData = {
        // 个人信息 - 直接映射，API字段已经是正确格式
        surname: (ocrData.surname as string) ?? '',
        given_name: (ocrData.given_name as string) ?? '',
        chinese_name: (ocrData.chinese_name as string) ?? '',

        // 日期字段 - API返回DD/MM/YYYY格式，表单就需要这个格式
        date_of_birth: (ocrData.dob as string) ?? (ocrData.date_of_birth as string) ?? '',

        // 地点和国籍 - 直接使用API值
        place_of_birth: (ocrData.place_of_birth as string) ?? '',
        nationality: (ocrData.nationality as string) ?? '',

        // 性别 - API返回F/M，直接使用
        sex: ocrData.sex as 'M' | 'F' | undefined,

        // 护照信息 - 直接映射
        passport_number: (ocrData.passport_number as string) ?? '',
        date_of_issue: (ocrData.date_of_issue as string) ?? '',
        place_of_issue: (ocrData.place_of_issue as string) ?? '',
        date_of_expiry:
          (ocrData.passport_expiry as string) ?? (ocrData.date_of_expiry as string) ?? '',
      }

      console.log('✅ 格式化完成:', formattedData)
      return formattedData
    } catch (error) {
      console.error('💥 格式化OCR数据失败:', error)
      return {
        surname: '',
        given_name: '',
        chinese_name: '',
        date_of_birth: '',
        place_of_birth: '',
        nationality: '',
        sex: undefined,
        passport_number: '',
        date_of_issue: '',
        place_of_issue: '',
        date_of_expiry: '',
      }
    }
  }

  /**
   * 取消当前OCR处理 - 对应旧版 cancelOCRProcessing 函数
   */
  const cancelProcessing = () => {
    if (currentController.value && canCancel.value) {
      currentController.value.abort()
      console.log('🚫 OCR处理已取消')
      ElMessage.info('OCR识别已取消')
      return true
    }
    return false
  }

  /**
   * 重试OCR识别
   * @param file 护照文件
   */
  const retryOCR = async (file: File): Promise<OCRResult> => {
    console.log('🔄 重试OCR识别...')
    return await recognizePassport(file)
  }

  /**
   * 简化的OCR成功处理 - 对应旧版直接填充逻辑
   * 支持字段级别的验证和反馈
   * @param ocrData OCR识别结果
   * @param onSuccess 成功填充回调函数
   * @param onFieldFilled 单个字段填充回调
   */
  const handleOCRSuccess = (
    ocrData: OCRPassportData,
    onSuccess: (data: OCRPassportData) => void,
    onFieldFilled?: (field: string, value: unknown, isValid: boolean) => void,
  ) => {
    console.log('🎯 处理OCR成功结果:', ocrData)

    // 字段级别验证和反馈
    if (onFieldFilled) {
      Object.entries(ocrData).forEach(([field, value]) => {
        if (value && typeof value === 'string' && value.trim() !== '') {
          const isValid = validateField(field, value)
          onFieldFilled(field, value, isValid)
        }
      })
    }

    // 直接填充表单，不显示确认弹窗 - 对应旧版逻辑
    onSuccess(ocrData)
  }

  /**
   * 字段验证 - 对每个字段进行基础验证
   */
  const validateField = (field: string, value: unknown): boolean => {
    if (!value || typeof value !== 'string' || value.trim() === '') {
      return false
    }

    switch (field) {
      case 'passport_number':
        return /^[A-Za-z0-9]{6,12}$/.test(value)
      case 'date_of_birth':
      case 'date_of_issue':
      case 'date_of_expiry':
        return /^\d{2}\/\d{2}\/\d{4}$/.test(value)
      case 'sex':
        return value === 'M' || value === 'F'
      case 'surname':
      case 'given_name':
        return value.length >= 1 && value.length <= 50
      case 'nationality':
      case 'place_of_birth':
      case 'place_of_issue':
        return value.length >= 2 && value.length <= 50
      default:
        return value.trim() !== ''
    }
  }

  return {
    // 状态
    isProcessing: readonly(isProcessing),
    processingFile: readonly(processingFile),
    canCancel: readonly(canCancel),

    // 方法
    recognizePassport,
    cancelProcessing,
    retryOCR,
    handleOCRSuccess,
    formatOCRDataToFormFields,
    validateField,
  }
}

/**
 * OCR状态指示器组合式函数 - 对应旧版状态显示逻辑
 * 增强功能：支持进度指示、取消按钮、错误状态
 */
export function useOCRStatus() {
  const { isProcessing, processingFile, canCancel, cancelProcessing } = useOCR()

  /**
   * 创建OCR状态指示器 - 对应旧版 ocrStatus 元素创建逻辑
   * @param container 容器元素
   * @param showCancel 是否显示取消按钮
   */
  const createStatusIndicator = (container: HTMLElement, showCancel: boolean = true) => {
    if (!isProcessing.value) return

    // 移除已存在的状态指示器
    removeStatusIndicator(container)

    // 创建新的状态指示器 - 对应旧版HTML结构
    const statusEl = document.createElement('div')
    statusEl.className = 'ocr-status-indicator'
    statusEl.innerHTML = `
      <div class="ocr-status-content">
        <div class="ocr-loading-spinner"></div>
        <div class="ocr-status-text">
          <span class="ocr-main-text">正在识别护照信息...</span>
          <span class="ocr-file-name">${processingFile.value}</span>
        </div>
        ${
          showCancel && canCancel.value
            ? '<button type="button" class="ocr-cancel-btn" title="取消识别">×</button>'
            : ''
        }
      </div>
    `

    // 添加取消事件监听
    if (showCancel && canCancel.value) {
      const cancelBtn = statusEl.querySelector('.ocr-cancel-btn')
      if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
          if (cancelProcessing()) {
            removeStatusIndicator(container)
          }
        })
      }
    }

    container.appendChild(statusEl)
  }

  /**
   * 移除状态指示器
   */
  const removeStatusIndicator = (container: HTMLElement) => {
    const statusEl = container.querySelector('.ocr-status-indicator')
    if (statusEl) {
      statusEl.remove()
    }
  }

  /**
   * 显示OCR错误状态
   */
  const showErrorStatus = (container: HTMLElement, message: string, onRetry?: () => void) => {
    removeStatusIndicator(container)

    // 🔒 安全修复：使用安全的 DOM 操作替代 innerHTML
    const errorEl = document.createElement('div')
    errorEl.className = 'ocr-error-indicator'

    const contentDiv = document.createElement('div')
    contentDiv.className = 'ocr-error-content'

    const iconDiv = document.createElement('div')
    iconDiv.className = 'ocr-error-icon'
    iconDiv.textContent = '⚠'

    const textDiv = document.createElement('div')
    textDiv.className = 'ocr-error-text'
    textDiv.textContent = message // 安全：使用 textContent 防止 XSS

    contentDiv.appendChild(iconDiv)
    contentDiv.appendChild(textDiv)

    if (onRetry) {
      const retryBtn = document.createElement('button')
      retryBtn.type = 'button'
      retryBtn.className = 'ocr-retry-btn'
      retryBtn.textContent = '重试'
      retryBtn.addEventListener('click', () => {
        removeStatusIndicator(container)
        onRetry()
      })
      contentDiv.appendChild(retryBtn)
    }

    errorEl.appendChild(contentDiv)

    container.appendChild(errorEl)

    // 3秒后自动移除错误提示
    setTimeout(() => {
      if (errorEl.parentElement) {
        errorEl.remove()
      }
    }, 3000)
  }

  return {
    isProcessing: readonly(isProcessing),
    processingFile: readonly(processingFile),
    canCancel: readonly(canCancel),
    createStatusIndicator,
    removeStatusIndicator,
    showErrorStatus,
  }
}
