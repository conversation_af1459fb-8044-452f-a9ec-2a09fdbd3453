import VisaFormView from '@/views/VisaFormView.vue'
import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/LoginView.vue'),
    meta: {
      title: '用户登录',
      requiresGuest: true, // 只允许未登录用户访问
    },
  },
  {
    path: '/',
    name: 'Home',
    redirect: '/visa-form',
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/visa-form',
    name: 'visa-form',
    component: VisaFormView,
    meta: {
      title: '申请签证',
      requiresAuth: true,
    },
  },
  {
    path: '/order-management',
    name: 'order-management',
    component: () => import('@/views/order/OrderManagementView.vue'),
    meta: {
      title: '订单管理',
      requiresAuth: true,
    },
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => import('@/views/DashboardView.vue'),
    meta: {
      title: '数据统计',
      requiresAuth: true,
    },
  },
  // 捕获所有未匹配的路由，重定向到登录页
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/login',
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})

// 路由守卫 - 延迟导入store避免初始化问题
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 越南签证自动化系统`
  }

  try {
    // 如果访问登录页，直接允许
    if (to.name === 'login') {
      next()
      return
    }

    // 动态导入store，确保Pinia已初始化
    const { useAuthStore } = await import('@/stores/auth')
    const { useSessionStore } = await import('@/stores/session')
    const authStore = useAuthStore()
    const sessionStore = useSessionStore()

    // 检查是否需要认证
    if (to.meta?.requiresAuth) {
      // 检查用户是否已登录
      if (!authStore.isAuthenticated) {
        // 尝试通过Token获取用户信息
        const isValidToken = await authStore.checkTokenValidity()

        if (!isValidToken) {
          // Token无效或不存在，跳转到登录页
          console.log('[Router] 认证失败，重定向到登录页')
          next({
            name: 'login',
            query: { redirect: to.fullPath },
          })
          return
        }

        // 🔧 新增：首次认证成功后启动会话监控
        if (isValidToken && !sessionStore.isMonitoringEnabled) {
          console.log('[Router] 首次认证成功，启动会话监控')
          sessionStore.startSessionMonitoring()
        }
      }
    }

    // 检查是否为仅限访客页面（如登录页）
    if (to.meta?.requiresGuest && authStore.isAuthenticated) {
      // 已登录用户不能访问登录页，跳转到首页
      next({ name: 'Home' })
      return
    }

    next()
  } catch (error) {
    console.error('[Router] 路由守卫执行失败:', error)
    // 如果store初始化失败，重定向到登录页作为安全措施
    if (to.name !== 'login') {
      next({ name: 'login' })
    } else {
      next()
    }
  }
})

export default router
