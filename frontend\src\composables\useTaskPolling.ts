/**
 * 订单轮询组合式函数 - 新架构版本
 * 🔥 适配新架构：通过FastAPI查询automation_logs获取任务状态
 */

import { computed, onMounted, onUnmounted, readonly, ref } from 'vue'

import { api } from '@/api/request'
import type { OrderInfo } from '@/api/types'
import { useApplicationStore } from '@/stores/application'
import { useAuthStore } from '@/stores/auth'
import { redirectToLogin } from '@/utils/navigation'

interface PollingOptions {
  interval?: number
  enabled?: boolean
}

interface TaskStatus {
  order_no: string
  status: 'processing' | 'success' | 'failed' | 'cancelled' | 'pending'
  message?: string
  task_type?: string
  celery_task_id?: string
  started_at?: string
  completed_at?: string
  updated_at: string
}

// 状态更新记录接口
interface StatusUpdateRecord {
  orderNo: string
  oldStatus: string
  newStatus: string
  timestamp: string
  message: string
}

// 轮询错误类型
interface PollingError {
  message?: string
  toString?: () => string
}

export function useTaskPolling(options: PollingOptions = {}) {
  const { interval = 30000, enabled = true } = options

  // 状态管理
  const isPolling = ref(false)
  const isConnected = ref(true)
  const isPageVisible = ref(true)
  const hasError = ref(false)
  const errorMessage = ref('')
  const canRetry = ref(true)

  // 订单和状态更新
  const orders = ref<OrderInfo[]>([])
  const statusUpdates = ref<StatusUpdateRecord[]>([])

  // 应用商店
  const applicationStore = useApplicationStore()
  // 🎯 使用认证store替代直接localStorage访问
  const authStore = useAuthStore()

  let pollingTimer: number | null = null

  /**
   * 查询单个任务的状态
   */
  const queryTaskStatus = async (orderNo: string): Promise<TaskStatus | null> => {
    try {
      // ✅ 依赖统一拦截器处理认证，移除手动token检查
      const response = await api.get(`/api/automation-logs/status/${orderNo}`)

      if (response.success) {
        return response.data as TaskStatus
      } else {
        return null
      }
    } catch (error) {
      // 静默处理错误，避免控制台噪音
      return null
    }
  }

  /**
   * 执行轮询
   */
  const performPolling = async () => {
    if (!enabled || !isPageVisible.value) {
      return
    }

    try {
      hasError.value = false
      errorMessage.value = ''

      // 🔥 实现真正的轮询逻辑：查询automation_logs状态
      const applications = applicationStore.applications

      // 🔥 修复：轮询所有处理中的任务，即使orderNo为undefined
      const processingApps = applications.filter((app) => app.automationStatus === 'processing')

      if (processingApps.length === 0) {
        isConnected.value = true
        return
      }

      // 批量查询任务状态
      for (const app of processingApps) {
        try {
          // 🔥 修复：如果orderNo为undefined，跳过查询
          if (!app.orderNo) {
            continue
          }

          const taskStatus = await queryTaskStatus(app.orderNo)

          if (taskStatus) {
            if (taskStatus.status !== app.automationStatus) {
              // 只更新有效的状态（排除pending状态）
              if (taskStatus.status !== 'pending') {
                // 更新自动化任务状态
                applicationStore.updateApplicationStatusByOrderNo(
                  app.orderNo,
                  taskStatus.status as 'processing' | 'success' | 'failed' | 'cancelled',
                )

                // 记录状态更新
                statusUpdates.value.unshift({
                  orderNo: app.orderNo,
                  oldStatus: app.automationStatus,
                  newStatus: taskStatus.status,
                  timestamp: new Date().toISOString(),
                  message: taskStatus.message ?? '自动化任务状态已更新',
                })

                // 限制状态更新记录数量
                if (statusUpdates.value.length > 50) {
                  statusUpdates.value = statusUpdates.value.slice(0, 50)
                }
              }
            }
          }
        } catch (error) {
          // 静默处理单个任务查询错误
        }
      }

      isConnected.value = true
    } catch (error: unknown) {
      let pollingError: PollingError = {}

      // 安全的错误消息提取
      if (typeof error === 'object' && error !== null && 'message' in error) {
        pollingError = error as PollingError
      } else if (typeof error === 'string') {
        pollingError.message = error
      } else if (error instanceof Error) {
        pollingError.message = error.message
      } else {
        // 对于其他类型的错误，使用默认消息
        pollingError.message = '轮询失败'
      }

      hasError.value = true
      errorMessage.value = pollingError.message ?? '轮询失败'
      isConnected.value = false
    }
  }

  /**
   * 开始轮询
   */
  const startPolling = () => {
    if (!enabled) {
      return
    }

    // ✅ 移除手动认证检查，依赖统一拦截器处理
    isPolling.value = true

    // 立即执行一次
    performPolling()

    // 设置定时器
    if (pollingTimer) {
      clearInterval(pollingTimer)
    }

    pollingTimer = window.setInterval(performPolling, interval)
  }

  /**
   * 停止轮询
   */
  const stopPolling = () => {
    isPolling.value = false

    if (pollingTimer) {
      clearInterval(pollingTimer)
      pollingTimer = null
    }
  }

  /**
   * 清除错误
   */
  const clearError = () => {
    hasError.value = false
    errorMessage.value = ''
    canRetry.value = true
  }

  /**
   * 获取最近的状态更新
   */
  const getRecentUpdates = (limit = 10) => {
    return statusUpdates.value.slice(0, limit)
  }

  /**
   * 页面可见性处理
   */
  const handleVisibilityChange = () => {
    isPageVisible.value = !document.hidden

    if (isPageVisible.value && isPolling.value) {
      // 页面重新可见时，立即执行一次轮询
      performPolling()
    }
  }

  /**
   * 监听认证状态变化
   */
  const watchAuthStatus = () => {
    // ✅ 使用authStore状态，移除直接localStorage操作
    const checkAuthStatus = () => {
      // 🔧 修复：检查token而不是完整的认证状态，避免误判
      const hasToken = !!authStore.token

      // 只有在确实没有token时才认为用户登出
      if (!hasToken && isPolling.value) {
        stopPolling()
        hasError.value = true
        errorMessage.value = '用户已登出'

        // 🔧 修复：自动跳转到登录页，而不是只显示错误消息
        redirectToLogin()
      } else if (hasToken && !isPolling.value && enabled) {
        clearError()
        // 延迟启动，避免频繁切换
        setTimeout(() => {
          if (enabled && !isPolling.value) {
            startPolling()
          }
        }, 1000)
      }
    }

    // 定期检查认证状态（每30秒）
    const authCheckInterval = setInterval(checkAuthStatus, 30000)

    return () => clearInterval(authCheckInterval)
  }

  // 生命周期管理
  onMounted(() => {
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 🔐 启动认证状态监听
    const stopAuthWatch = watchAuthStatus()

    // ✅ 简化启动逻辑，依赖统一拦截器处理认证
    if (enabled) {
      startPolling()
    }

    // 清理函数
    onUnmounted(() => {
      stopPolling()
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      stopAuthWatch()
    })
  })

  // 计算属性
  const activeTasksCount = computed(() => {
    return applicationStore.applications.filter((app) => app.automationStatus === 'processing')
      .length
  })

  const completedTasksCount = computed(() => {
    return applicationStore.applications.filter(
      (app) =>
        app.automationStatus === 'success' ||
        app.automationStatus === 'failed' ||
        app.automationStatus === 'cancelled',
    ).length
  })

  // 🔧 调试函数：手动测试轮询
  const debugPolling = async () => {
    await performPolling()
  }

  return {
    // 状态
    isPolling: readonly(isPolling),
    isConnected: readonly(isConnected),
    isPageVisible: readonly(isPageVisible),
    hasError: readonly(hasError),
    errorMessage: readonly(errorMessage),
    canRetry: readonly(canRetry),

    // 数据
    orders: readonly(orders),
    statusUpdates: readonly(statusUpdates),

    // 计算属性
    activeTasksCount,
    completedTasksCount,

    // 方法
    startPolling,
    stopPolling,
    performPolling,
    clearError,
    getRecentUpdates,
    queryTaskStatus,
    debugPolling, // 🔧 调试方法
  }
}
