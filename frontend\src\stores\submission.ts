import { api } from '@/api/request'
import type { DuplicateCheckResponse } from '@/api/types'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

// 提交状态枚举
export const SubmissionState = {
  IDLE: 'idle' as const,
  SUBMITTING: 'submitting' as const,
  SUCCESS: 'success' as const,
  FAILED: 'failed' as const,
} as const

export type SubmissionStateType = (typeof SubmissionState)[keyof typeof SubmissionState]

// 重复检查结果接口
export interface DuplicateCheckResult {
  exists: boolean
  can_resubmit?: boolean
  warning_type?: 'success' | 'pending' | 'rejected' | 'failed' | 'none' | 'error'
  application_number?: string
  submission_time?: string
  status?: string
  approval_status?: string
  order_no?: string
}

export const useSubmissionStore = defineStore('submission', () => {
  // 提交状态，对应旧版isSubmitting
  const submissionState = ref<SubmissionStateType>(SubmissionState.IDLE)

  // 🔥 优化：简化本地检查，只用于防止快速重复点击
  // 仅存储最近1分钟内提交的护照号，防止用户快速重复点击同一表单
  const recentSubmissions = ref<Map<string, number>>(new Map())

  // 当前正在提交的护照号
  const currentSubmittingPassport = ref<string | null>(null)

  // 计算属性：是否正在提交
  const isSubmitting = computed(() => submissionState.value === SubmissionState.SUBMITTING)

  // 计算属性：是否空闲状态
  const isIdle = computed(() => submissionState.value === SubmissionState.IDLE)

  // 清理过期的本地提交记录（超过1分钟）
  const cleanupRecentSubmissions = () => {
    const now = Date.now()
    const oneMinuteAgo = now - 60 * 1000 // 1分钟前

    for (const [passport, timestamp] of recentSubmissions.value.entries()) {
      if (timestamp < oneMinuteAgo) {
        recentSubmissions.value.delete(passport)
      }
    }
  }

  // 🔥 简化：检查是否为快速重复提交（仅防止1分钟内的重复点击）
  function isQuickDuplicateSubmission(passportNumber: string): boolean {
    cleanupRecentSubmissions()

    const lastSubmission = recentSubmissions.value.get(passportNumber)
    if (lastSubmission) {
      const timeDiff = Date.now() - lastSubmission
      const isQuickDuplicate = timeDiff < 60 * 1000 // 1分钟内

      // 🔧 修复：移除调试日志

      return isQuickDuplicate
    }

    return false
  }

  // 开始提交流程
  function startSubmission(passportNumber: string) {
    if (isSubmitting.value) {
      // 🔧 修复：移除调试日志
      throw new Error('申请正在提交中，请勿重复点击提交按钮。')
    }

    submissionState.value = SubmissionState.SUBMITTING
    currentSubmittingPassport.value = passportNumber
    // 🔧 修复：移除调试日志
  }

  // 提交成功，记录时间戳
  function markSubmissionSuccess(passportNumber: string) {
    submissionState.value = SubmissionState.SUCCESS

    // 🔥 优化：只记录时间戳，用于防止快速重复点击
    recentSubmissions.value.set(passportNumber, Date.now())

    // 🔧 修复：移除调试日志

    // 延迟重置状态，允许继续提交
    setTimeout(() => {
      resetSubmissionState()
    }, 500)
  }

  // 提交失败，重置状态
  function markSubmissionFailed(error?: string) {
    submissionState.value = SubmissionState.FAILED
    console.error('❌ 提交失败:', error)

    // 延迟重置状态，允许重试
    setTimeout(() => {
      resetSubmissionState()
    }, 1000)
  }

  // 重置提交状态
  function resetSubmissionState() {
    submissionState.value = SubmissionState.IDLE
    currentSubmittingPassport.value = null
    // 🔧 修复：移除调试日志
  }

  // 服务器端重复检查，使用统一的API封装层
  async function checkServerDuplicate(passportNumber: string): Promise<DuplicateCheckResult> {
    try {
      // 🔧 修复：移除调试日志

      const response = await api.get<DuplicateCheckResponse>(
        `/api/visa/check-duplicate/${passportNumber}`,
      )

      // 🔧 修复：移除调试日志

      // 处理ApiResponse结构
      const result = response.data ?? (response as unknown as Record<string, unknown>)

      // 🔧 修复：移除调试日志

      const duplicateResult: DuplicateCheckResult = {
        exists: typeof result.exists === 'boolean' ? result.exists : false,
        can_resubmit: typeof result.can_resubmit === 'boolean' ? result.can_resubmit : undefined,
        warning_type:
          result.warning_type === 'success' ||
          result.warning_type === 'pending' ||
          result.warning_type === 'rejected' ||
          result.warning_type === 'failed' ||
          result.warning_type === 'none' ||
          result.warning_type === 'error'
            ? result.warning_type
            : undefined,
        application_number:
          typeof result.application_number === 'string' ? result.application_number : undefined,
        submission_time:
          typeof result.submission_time === 'string' ? result.submission_time : undefined,
        status: typeof result.status === 'string' ? result.status : undefined,
        approval_status:
          typeof result.approval_status === 'string' ? result.approval_status : undefined,
        order_no: typeof result.order_no === 'string' ? result.order_no : undefined,
      }

      // 🔧 修复：移除调试日志
      return duplicateResult
    } catch (error: unknown) {
      console.error('❌ 服务器重复检查API调用失败，详细错误:', error)

      const errorMessage = error instanceof Error ? error.message : '未知错误'
      const errorName = error instanceof Error ? error.constructor.name : '未知类型'
      const errorStack = error instanceof Error ? error.stack : '无堆栈信息'

      console.error('❌ 错误类型:', errorName)
      console.error('❌ 错误消息:', errorMessage)
      console.error('❌ 错误堆栈:', errorStack)

      if (errorMessage === 'UNAUTHORIZED') {
        console.log('重复检查时发现会话过期')
        throw new Error('UNAUTHORIZED')
      } else {
        console.warn('⚠️ 检查重复提交失败，返回默认值继续流程:', error)
        return { exists: false }
      }
    }
  }

  // 强制重新提交标记（用户确认重复提交时）
  function markForceResubmit() {
    // 🔧 修复：移除调试日志
  }

  // 🔥 简化：清空快速重复检查记录
  function clearRecentSubmissions() {
    recentSubmissions.value.clear()
    // 🔧 修复：移除调试日志
  }

  // 获取最近提交记录（用于调试）
  const recentSubmissionsList = computed(() => {
    cleanupRecentSubmissions()
    return Array.from(recentSubmissions.value.entries()).map(([passport, timestamp]) => ({
      passport,
      timestamp: new Date(timestamp).toLocaleString(),
    }))
  })

  return {
    // 状态
    submissionState,
    currentSubmittingPassport,
    recentSubmissions,

    // 计算属性
    isSubmitting,
    isIdle,
    recentSubmissionsList,

    // 方法
    isQuickDuplicateSubmission, // 重命名：更准确反映功能
    startSubmission,
    markSubmissionSuccess,
    markSubmissionFailed,
    resetSubmissionState,
    checkServerDuplicate,
    markForceResubmit,
    clearRecentSubmissions, // 重命名：更准确反映功能
  }
})
