<template>
  <div class="order-management">
    <!-- 订单查询卡片 - 合并标题和筛选 -->
    <el-card class="order-query-section" shadow="hover">
      <template #header>
        <div class="section-header">
          <h2>我的订单</h2>
        </div>
      </template>
      <el-form ref="filterFormRef" :model="filterForm" inline label-width="80px">
        <!-- 🔧 优化：将所有筛选项排列在一行，增加合适间距 -->
        <el-row :gutter="138">
          <el-col :xs="24" :sm="12" :md="4" :lg="4">
            <el-form-item label="订单编号">
              <el-input
                v-model="filterForm.order_no"
                placeholder="输入订单编号"
                clearable
                style="width: 160px"
              />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="4" :lg="4">
            <el-form-item label="申请编号">
              <el-input
                v-model="filterForm.application_number"
                placeholder="输入越南官方编号"
                clearable
                style="width: 160px"
              />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="4" :lg="4">
            <el-form-item label="签证状态">
              <el-select
                v-model="filterForm.status"
                placeholder="选择签证状态"
                clearable
                style="width: 160px"
                @change="handleStatusChange"
              >
                <el-option
                  v-for="status in visaStatusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="4" :lg="4">
            <el-form-item label="姓名">
              <el-input
                v-model="filterForm.applicant_name"
                placeholder="输入中文名或英文名"
                clearable
                style="width: 160px"
              />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="6" :lg="6">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                clearable
                style="width: 220px"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <div class="filter-actions">
              <el-button type="primary" @click="handleSearch" :loading="loading"> 查询 </el-button>
              <el-button @click="handleReset">重置</el-button>
              <el-button type="success" @click="handleRefresh" :icon="Refresh" :loading="loading">
                刷新
              </el-button>
              <el-button
                type="primary"
                @click="handleExport"
                :icon="Download"
                :loading="exportLoading"
              >
                导出订单表
              </el-button>
              <el-button
                v-if="hasError && canRetry"
                type="warning"
                @click="handleRetry"
                :icon="Connection"
              >
                重连
              </el-button>
            </div>
          </el-col>
        </el-row>

        <!-- 错误提示 -->
        <el-row v-if="hasError">
          <el-col :span="24">
            <el-alert
              :title="errorMessage"
              type="warning"
              :closable="true"
              show-icon
              class="error-alert"
              @close="clearError"
            >
              <template v-if="canRetry" #default>
                {{ errorMessage }}
                <el-button type="text" size="small" @click="handleRetry" style="margin-left: 8px">
                  立即重试
                </el-button>
              </template>
            </el-alert>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 订单列表 -->
    <el-card class="order-list" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>订单列表</h3>
          <div class="header-actions">
            <el-tag v-if="recentUpdates.length > 0" type="success" size="small">
              最近更新: {{ recentUpdates.length }}
            </el-tag>
          </div>
        </div>
      </template>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-section">
        <el-skeleton :rows="3" animated />
        <div class="loading-text">正在加载订单数据...</div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="displayOrders.length === 0" class="empty-section">
        <el-empty description="暂无订单数据">
          <template #image>
            <el-icon size="64" color="#909399">
              <Document />
            </el-icon>
          </template>
          <el-button type="primary" @click="router.push('/visa-form')"> 立即申请签证 </el-button>
        </el-empty>
      </div>

      <!-- 订单表格列表 -->
      <div v-else class="order-table">
        <el-table
          :data="displayOrders"
          stripe
          size="small"
          table-layout="fixed"
          class="compact-table"
          style="width: 100%"
        >
          <!-- 创建时间列 -->
          <el-table-column prop="created_at" label="创建时间" width="120" align="left">
            <template #default="{ row }">
              <div class="time-cell">
                {{ formatTime(row.created_at) }}
              </div>
            </template>
          </el-table-column>

          <!-- 订单编号列 - 缩小宽度 -->
          <el-table-column prop="order_no" label="订单编号" width="140">
            <template #default="{ row }">
              <div class="order-no-cell">
                <span class="order-number">{{ formatOrderDisplay(row.order_no) }}</span>
                <el-button
                  type="primary"
                  text
                  size="small"
                  @click="copyOrderNo(row.order_no)"
                  :icon="CopyDocument"
                  class="copy-btn"
                />
              </div>
            </template>
          </el-table-column>

          <!-- 申请编号列 - 紧贴订单编号 -->
          <el-table-column prop="application_number" label="申请编号" width="130" align="center">
            <template #default="{ row }">
              <div class="application-number">
                {{ row.application_number || '---' }}
              </div>
            </template>
          </el-table-column>

          <!-- 申请人姓名列 -->
          <el-table-column label="申请人姓名" width="100" align="center">
            <template #default="{ row }">
              <div class="applicant-name">
                {{ getChineseNamePriority(row) }}
              </div>
            </template>
          </el-table-column>

          <!-- 护照号码列 -->
          <el-table-column prop="passport_number" label="护照号码" width="105" align="center">
            <template #default="{ row }">
              <div class="passport-cell">
                {{ row.passport_number }}
              </div>
            </template>
          </el-table-column>

          <!-- 签证状态列 -->
          <el-table-column prop="status" label="签证状态" width="90" align="center">
            <template #default="{ row }">
              <el-tag
                :type="getVisaStatusType((row as any).current_visa_status)"
                size="small"
                :class="{ 'status-updated': isRecentlyUpdated(row.order_no) }"
              >
                {{ getVisaStatusText((row as any).current_visa_status) }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 签证类型列 -->
          <el-table-column label="签证类型" width="70" align="center">
            <template #default="{ row }">
              <div class="visa-type">
                {{ getVisaType(row) }}
              </div>
            </template>
          </el-table-column>

          <!-- 签证有效期列 -->
          <el-table-column label="签证有效期" width="85" align="center">
            <template #default="{ row }">
              <div class="visa-validity">
                {{ getVisaValidity(row) }}
              </div>
            </template>
          </el-table-column>

          <!-- 签证生效日期列 -->
          <el-table-column label="签证生效日期" width="105" align="center">
            <template #default="{ row }">
              <div class="visa-start-date">
                {{ getVisaStartDate(row) }}
              </div>
            </template>
          </el-table-column>

          <!-- 入境口岸列 -->
          <el-table-column label="入境口岸" width="90" align="center">
            <template #default="{ row }">
              <div class="entry-gate">
                {{ getEntryGate(row) }}
              </div>
            </template>
          </el-table-column>

          <!-- 加急类型列 -->
          <el-table-column label="加急类型" width="75" align="center">
            <template #default="{ row }">
              <div class="expedited-type">
                {{ getExpeditedType(row) }}
              </div>
            </template>
          </el-table-column>

          <!-- 出行目的列 -->
          <el-table-column label="出行目的" width="75" align="center">
            <template #default="{ row }">
              <div class="purpose">
                {{ getPurpose(row) }}
              </div>
            </template>
          </el-table-column>

          <!-- 详情列 -->
          <el-table-column label="详情" width="90" align="center">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="handleViewDetail(row.order_no)"
                :icon="View"
              >
                详情
              </el-button>
            </template>
          </el-table-column>

          <!-- 下载列 -->
          <el-table-column label="下载" width="80" align="center">
            <template #default="{ row }">
              <div class="download-actions">
                <el-link type="primary" underline="never" @click="handleDownload(row.order_no)">
                  下载
                </el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div v-if="displayOrders.length > 0" class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total_items"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 订单详情弹窗 -->
    <OrderDetailDialog
      :visible="detailDialogVisible"
      :order-no="selectedOrderNo"
      @update:visible="detailDialogVisible = $event"
      @download="handleDownload"
    />

    <!-- 状态更新通知 -->
    <NotificationToast
      v-for="notification in activeNotifications"
      :key="notification.id"
      :visible="true"
      :type="notification.type"
      :title="notification.title"
      :message="notification.message"
      :order-no="notification.orderNo"
      @close="removeNotification(notification.id)"
    />
  </div>
</template>

<script setup lang="ts">
import OrderAPI from '@/api/order'
import { api } from '@/api/request'
import type { OrderInfo, OrderStatus, PaginationInfo } from '@/api/types'
import NotificationToast from '@/components/common/NotificationToast.vue'
import OrderDetailDialog from '@/components/order/OrderDetailDialog.vue'
import { useTaskPolling as useOrderPolling } from '@/composables/useTaskPolling'
import { OrderNumberGenerator, OrderStatusHelper } from '@/utils/orderNumber'
import {
  Connection,
  CopyDocument,
  Document,
  Download,
  Refresh,
  View,
} from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'

// Router
const router = useRouter()

// 订单轮询 - 核心功能
const {
  orders: polledOrders,
  hasError,
  errorMessage,
  canRetry,
  clearError,
  statusUpdates,
  getRecentUpdates,
  startPolling,
  stopPolling,
  performPolling,
} = useOrderPolling({
  interval: 30000, // 30 seconds as specified
  enabled: true,
})

// 本地状态
const loading = ref(false)
const exportLoading = ref(false)
const manualOrders = ref<OrderInfo[]>([])

// 筛选表单
const filterFormRef = ref<FormInstance>()
const filterForm = reactive({
  order_no: '',
  application_number: '',
  status: '' as string,
  applicant_name: '', // 🔧 新增：申请人姓名筛选
})

// 日期范围
const dateRange = ref<[string, string] | undefined>(undefined)

// 分页信息
const pagination = reactive<PaginationInfo>({
  current_page: 1,
  total_pages: 0,
  total_items: 0,
  has_prev: false,
  has_next: false,
  page_size: 20,
})

// 详情弹窗相关
const detailDialogVisible = ref(false)
const selectedOrderNo = ref('')

// 通知管理
const activeNotifications = ref<
  Array<{
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message: string
    orderNo?: string
  }>
>([])

// 计算属性
const visaStatusOptions = computed(() => [
  { label: '提交成功', value: 'submitted' },
  { label: '提交失败', value: 'submit_failure' },
  { label: '待补资料', value: 'additional_info_required' },
  { label: '已出签', value: 'approved' },
  { label: '已拒签', value: 'denied' },
])

// 显示的订单列表（轮询数据优先，手动查询为补充）
const displayOrders = computed(() => {
  // 如果有轮询数据且没有应用筛选，优先使用轮询数据
  const hasFilters =
    filterForm.order_no ||
    filterForm.application_number ||
    filterForm.status ||
    filterForm.applicant_name || // 🔧 新增：姓名筛选检查
    dateRange.value

  if (!hasFilters && polledOrders.value.length > 0) {
    return [...polledOrders.value] // 转换为可变数组
  }

  // 否则使用手动查询数据，并应用前端筛选
  let filteredOrders = [...manualOrders.value]

  // 🔧 新增：前端姓名筛选逻辑
  if (filterForm.applicant_name) {
    const nameFilter = filterForm.applicant_name.toLowerCase().trim()
    filteredOrders = filteredOrders.filter(order => {
      const chineseName = getChineseNamePriority(order).toLowerCase()
      const englishName = getEnglishNamePriority(order).toLowerCase()
      return chineseName.includes(nameFilter) || englishName.includes(nameFilter)
    })
  }

  return filteredOrders
})

// 最近状态更新
const recentUpdates = computed(() => getRecentUpdates(5))

// 检查订单是否最近更新过
const isRecentlyUpdated = (orderNo: string): boolean => {
  return recentUpdates.value.some((update) => update.orderNo === orderNo)
}

// 方法
const loadOrders = async (): Promise<void> => {
  try {
    loading.value = true

    const params = {
      page: pagination.current_page,
      limit: pagination.page_size,
      order_no: filterForm.order_no ?? undefined,
      application_number: filterForm.application_number ?? undefined,
      status: (filterForm.status as OrderStatus) ?? undefined,
      date_from: dateRange.value?.[0] ?? undefined,
      date_to: dateRange.value?.[1] ?? undefined,
    }

    const response = await OrderAPI.queryOrders(params)

    if (response.success && response.data) {
      manualOrders.value = response.data.orders || []

      // 更新分页信息
      if (response.data.pagination) {
        Object.assign(pagination, response.data.pagination)
      }
    } else {
      ElMessage.error(response.message ?? '查询订单失败')
      manualOrders.value = []
    }
  } catch (error: unknown) {
    console.error('加载订单失败:', error)

    const errorObj = error as { error?: string; message?: string }

    if (errorObj.error === 'Unauthorized') {
      ElMessage.error('登录已过期，请重新登录')
    } else {
      const errorMessage =
        errorObj.message ?? (error instanceof Error ? error.message : '查询订单失败')
      ElMessage.error(errorMessage)
    }
    manualOrders.value = []
  } finally {
    loading.value = false
  }
}

const handleSearch = (): void => {
  pagination.current_page = 1

  // 如果有筛选条件，停止轮询，使用手动查询
  const hasFilters =
    filterForm.order_no ||
    filterForm.application_number ||
    filterForm.status ||
    filterForm.applicant_name || // 🔧 新增：姓名筛选检查
    dateRange.value

  if (hasFilters) {
    stopPolling()
    loadOrders()
  } else {
    // 无筛选条件，重新启动轮询
    loadOrders()
    nextTick(() => {
      startPolling()
    })
  }
}

const handleReset = (): void => {
  filterFormRef.value?.resetFields()
  filterForm.order_no = ''
  filterForm.application_number = ''
  filterForm.status = '' as string
  filterForm.applicant_name = '' // 🔧 新增：重置姓名筛选
  dateRange.value = undefined
  pagination.current_page = 1

  // 重置后启动轮询
  loadOrders()
  nextTick(() => {
    startPolling()
  })
}

const handleRefresh = (): void => {
  const hasFilters =
    filterForm.order_no ||
    filterForm.application_number ||
    filterForm.status ||
    filterForm.applicant_name || // 🔧 新增：姓名筛选检查
    dateRange.value

  if (hasFilters) {
    loadOrders()
  } else {
    // 手动触发轮询刷新
    performPolling()
  }
}

const handleExport = async (): Promise<void> => {
  try {
    exportLoading.value = true

    // 构建导出参数 (仅支持后端API支持的参数)
    const params = {
      format: 'excel',
      ...(filterForm.status && { status: filterForm.status }),
      ...(dateRange.value?.[0] && { date_from: dateRange.value[0] }),
      ...(dateRange.value?.[1] && { date_to: dateRange.value[1] }),
    }

    // ✅ 使用统一API对象，使用统一认证和错误处理
    const response = await api.get('/api/visa/export', {
      params,
      responseType: 'blob',
    })

    // 处理blob响应 - 当responseType为'blob'时，response直接是Blob对象
    const blob = response as unknown as Blob
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 生成默认文件名
    const filename = `越南签证订单表_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.download = filename

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error: unknown) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const handleRetry = (): void => {
  performPolling()
}

const handlePageChange = (page: number): void => {
  pagination.current_page = page
  loadOrders()
}

const handlePageSizeChange = (size: number): void => {
  pagination.page_size = size
  pagination.current_page = 1
  loadOrders()
}

const handleViewDetail = (orderNo: string): void => {
  selectedOrderNo.value = orderNo
  detailDialogVisible.value = true
}

const handleDownload = (orderNo: string): void => {
  // 🔧 修复：移除调试日志
  ElMessage.info('下载功能开发中...')
}

const handleStatusChange = (value: string): void => {
  // 🔧 修复：移除调试日志
  handleSearch()
}

// 表格工具方法
const formatOrderDisplay = (orderNo: string): string => {
  return OrderNumberGenerator.formatDisplay(orderNo)
}

const copyOrderNo = async (orderNo: string): Promise<void> => {
  try {
    await navigator.clipboard.writeText(orderNo)
    ElMessage.success('订单编号已复制')
  } catch {
    ElMessage.error('复制失败')
  }
}

const getStatusText = (status: string): string => {
  return OrderStatusHelper.getStatusText(status as OrderStatus)
}

const formatTime = (timeStr: string): string => {
  try {
    return new Date(timeStr).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch {
    return timeStr
  }
}

// 新增数据提取工具函数
const getChineseNamePriority = (row: OrderInfo): string => {
  // 现在申请人姓名直接在row对象中
  return row.applicant_name || '---'
}

const getEnglishNamePriority = (row: OrderInfo): string => {
  // 🔧 新增：从application_data中提取英文姓名
  const applicationData = row.application_data as unknown as Record<string, unknown>
  const formData = (applicationData?.form_data as Record<string, unknown>) || applicationData

  const surname = formData?.surname as string
  const givenName = formData?.given_name as string

  if (surname && givenName) {
    return `${surname} ${givenName}`
  } else if (surname) {
    return surname
  } else if (givenName) {
    return givenName
  }

  return '---'
}

const getVisaType = (row: OrderInfo): string => {
  const visaEntryType = (row as OrderInfo & { visa_entry_type?: string }).visa_entry_type

  if (visaEntryType === 'Single-entry') return '单次'
  if (visaEntryType === 'Multiple-entry') return '多次'
  return '---'
}

const getVisaValidity = (row: OrderInfo): string => {
  const validity = (row as OrderInfo & { visa_validity_duration?: string }).visa_validity_duration
  return validity ?? '---'
}

const getVisaStartDate = (row: OrderInfo): string => {
  const startDate = (row as OrderInfo & { visa_start_date?: string }).visa_start_date

  if (!startDate) return '---'

  try {
    // 处理多种日期格式：DD/MM/YYYY, YYYY-MM-DD
    if (startDate.includes('/')) {
      const [day, month, year] = startDate.split('/')
      return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
    }
    return startDate
  } catch {
    return startDate
  }
}

const getEntryGate = (row: OrderInfo): string => {
  const entryGate = (row as OrderInfo & { intended_entry_gate?: string }).intended_entry_gate

  if (!entryGate) return '---'

  // 简化显示机场名称
  if (entryGate.includes('Tan Son Nhat')) return '胡志明市'
  if (entryGate.includes('Noi Bai')) return '河内'
  if (entryGate.includes('Da Nang')) return '岘港'
  if (entryGate.includes('Phu Quoc')) return '富国岛'
  if (entryGate.includes('Cam Ranh')) return '金兰湾'

  return entryGate.length > 8 ? entryGate.substring(0, 8) + '...' : entryGate
}

const getExpeditedType = (row: OrderInfo): string => {
  // 🎯 精确数据访问：根据后端order_repository.py第401行的确切结构
  // application_data = {"form_data": form_snapshot or {}}
  const applicationData = row.application_data as { form_data?: Record<string, unknown> }
  const formData = applicationData?.form_data

  if (!formData) {
    return '---'
  }

  const expeditedType = formData.expedited_type as string | undefined

  if (!expeditedType) {
    return '---'
  }

  // 🎯 严格匹配后端backend/routes/visa/export.py中的expedited_map
  const typeMap: Record<string, string> = {
    '1days': '1工加急',
    '2days': '2工加急',
    '3days': '3工加急',
    '4days': '4工出签',
    normal: '普通',
  }

  return typeMap[expeditedType] || expeditedType
}

const getPurpose = (row: OrderInfo): string => {
  const purpose = (row as OrderInfo & { purpose_of_entry?: string }).purpose_of_entry

  const purposeMap: Record<string, string> = {
    Tourist: '旅游',
    Business: '商务',
    'Visiting relatives': '探亲',
    Working: '工作',
    Other: '其他',
  }

  return purposeMap[purpose ?? ''] ?? purpose ?? '---'
}

// 签证状态相关函数 - 使用真实的签证状态
const getVisaStatusType = (
  visaStatus: string,
): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  // 根据真实签证状态返回类型
  if (!visaStatus) return 'info'

  if (visaStatus === 'submitted') return 'primary'
  if (visaStatus === 'approved') return 'success'
  if (visaStatus === 'issued') return 'success'
  if (visaStatus === 'rejected') return 'danger'
  if (visaStatus === 'cancelled') return 'info'

  return 'info'
}

const getVisaStatusText = (visaStatus: string): string => {
  // 真实签证状态文本映射 -严格匹配后端数据库字段
  if (!visaStatus) return '待提交'

  const statusMap: Record<string, string> = {
    submitted: '提交成功',
    submit_failure: '提交失败',
    additional_info_required: '待补资料',
    approved: '已出签',
    denied: '已拒签',
  }

  return statusMap[visaStatus] || visaStatus
}

// 通知管理
const showNotification = (
  type: 'success' | 'error' | 'warning' | 'info',
  title: string,
  message: string,
  orderNo?: string,
): void => {
  const notification = {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    type,
    title,
    message,
    orderNo,
  }

  activeNotifications.value.push(notification)

  // 限制同时显示的通知数量
  if (activeNotifications.value.length > 3) {
    activeNotifications.value = activeNotifications.value.slice(-3)
  }
}

const removeNotification = (id: string): void => {
  const index = activeNotifications.value.findIndex((n) => n.id === id)
  if (index > -1) {
    activeNotifications.value.splice(index, 1)
  }
}

// 监听状态更新并显示通知
watch(
  statusUpdates,
  (newUpdates) => {
    if (newUpdates.length > 0) {
      const latestUpdate = newUpdates[0]
      const statusText = getStatusText(latestUpdate.newStatus)

      let notificationType: 'success' | 'error' | 'warning' | 'info' = 'info'
      if (['downloaded', 'paid'].includes(latestUpdate.newStatus)) {
        notificationType = 'success'
      } else if (latestUpdate.newStatus === 'failed') {
        notificationType = 'error'
      } else if (['success', 'approved'].includes(latestUpdate.newStatus)) {
        notificationType = 'success'
      }

      showNotification(
        notificationType,
        '订单状态更新',
        `订单状态已更新为: ${statusText}`,
        latestUpdate.orderNo,
      )
    }
  },
  { deep: true },
)

// 生命周期
onMounted(() => {
  // 初始加载
  loadOrders()

  // 启动轮询（如果没有筛选条件）
  nextTick(() => {
    startPolling()
  })
})

// 监听器
watch(dateRange, () => {
  if (dateRange.value) {
    handleSearch()
  }
})
</script>

<style scoped>
.order-management {
  padding: 10px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 订单查询卡片 - 合并样式 */
.order-query-section {
  margin-bottom: 16px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
}

.order-query-section .section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.order-query-section .section-header h2 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 20px;
  font-weight: 600;
}

.order-query-section .el-form {
  padding: 8px 0;
}

.order-query-section .el-form-item {
  margin-bottom: 16px;
  margin-right: 16px;
}

.order-query-section .el-form-item__label {
  width: 80px !important;
  text-align: right;
  padding-right: 12px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.order-query-section .el-input,
.order-query-section .el-select,
.order-query-section .el-date-editor {
  width: 200px;
}

.filter-actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
  padding-left: 0;
  justify-content: flex-start;
}

.error-alert {
  margin-top: 16px;
}

.order-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 18px;
  font-weight: 600;
}

.loading-section {
  padding: 40px 0;
  text-align: center;
}

.loading-text {
  margin-top: 16px;
  color: var(--el-text-color-regular);
}

.empty-section {
  padding: 60px 0;
  text-align: center;
}

.order-table {
  width: 100%;

  .compact-table {
    border-radius: 8px;
    overflow: hidden;
    width: 100%; /* 占满整个容器宽度 */

    :deep(.el-table__header) {
      background-color: #f8f9fa;
    }

    :deep(.el-table__header-wrapper) {
      overflow: visible;
    }

    :deep(.el-table__body-wrapper) {
      overflow-x: hidden; /* 彻底禁用横向滚动 */
    }

    :deep(.el-table__row) {
      &:hover > td {
        background-color: #f5f7fa !important;
      }

      &.status-updated > td {
        background-color: #f0f9ff !important;
      }
    }

    :deep(.el-table__cell) {
      padding: 6px 3px; /* 单元格内边距：上下6px，左右3px */
      text-align: center; /* 文字居中对齐 */
      border-right: 1px solid #ebeef5; /* 右边框：1px实线，颜色#ebeef5 */
      white-space: nowrap; /* 文字不换行 */
      overflow: hidden; /* 超出部分隐藏 */
      text-overflow: ellipsis; /* 超出部分显示省略号(...) */
      font-size: 14px; /* 字体大小 */
    }

    :deep(.el-table__header-wrapper .el-table__cell) {
      font-size: 12px;
      font-weight: bold; /* 🔥 表头加粗效果 */
      background-color: #f8f9fa;
      padding: 8px 3px;
      min-height: 40px;
      line-height: 1.2;
      color: #2c3e50; /* 增加对比度 */

      .cell {
        padding: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    :deep(.el-table__fixed) {
      box-shadow: 0 0 10px rgb(0 0 0 / 12%);
    }

    :deep(.el-table__header-wrapper .el-table__cell .cell) {
      font-weight: bold !important;
    }
  }

  .order-no-cell {
    display: flex;
    align-items: center;
    gap: 4px;

    .order-number {
      font-family: Monaco, Menlo, 'Ubuntu Mono', monospace;
      font-size: 12px;
      color: #2563eb;
      font-weight: 600;
    }

    .copy-btn {
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    &:hover .copy-btn {
      opacity: 1;
    }
  }

  .application-number,
  .applicant-name,
  .passport-cell,
  .visa-type,
  .visa-validity,
  .visa-start-date,
  .entry-gate,
  .expedited-type,
  .purpose {
    font-size: 12px;
    color: var(--el-text-color-regular);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .applicant-name {
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .passport-cell {
    font-family: Monaco, Menlo, 'Ubuntu Mono', monospace;
  }

  .visa-type {
    font-weight: 500;
  }

  .time-cell {
    font-size: 11px;
    color: var(--el-text-color-regular);
  }

  .download-actions {
    display: flex;
    justify-content: center;
    align-items: center;

    .el-button {
      padding: 4px 8px;
      font-size: 12px;
    }

    .no-action {
      color: var(--el-text-color-placeholder);
      font-size: 12px;
    }
  }

  :deep(.el-tag) {
    font-size: 11px;
    padding: 2px 6px;

    &.status-updated {
      animation: pulse 2s infinite;
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgb(34 197 94 / 40%);
  }

  50% {
    box-shadow: 0 0 0 4px rgb(34 197 94 / 10%);
  }
}

.pagination-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (width <= 768px) {
  .order-management {
    padding: 12px;
  }

  .order-query-section .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .filter-actions {
    flex-direction: column;
  }

  .filter-actions .el-button {
    width: 100%;
  }

  .order-table .compact-table {
    font-size: 11px;

    :deep(.el-table__cell) {
      padding: 6px 2px; /* 进一步减少padding */
    }
  }

  .order-table .order-no-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;

    .order-number {
      font-size: 10px;
    }
  }

  .order-table .application-number,
  .order-table .applicant-name,
  .order-table .passport-cell,
  .order-table .visa-type,
  .order-table .visa-validity,
  .order-table .visa-start-date,
  .order-table .entry-gate,
  .order-table .expedited-type,
  .order-table .purpose {
    font-size: 10px;
  }

  .order-table .time-cell {
    font-size: 10px;
  }

  .order-table .download-actions .el-button {
    padding: 2px 4px;
    font-size: 10px;
  }

  :deep(.el-tag) {
    font-size: 10px;
    padding: 1px 4px;
  }
}
</style>
