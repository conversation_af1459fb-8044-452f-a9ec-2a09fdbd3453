import type { ApiResponse, LoginRequest, LoginResponse, UserInfoResponse } from '@/types/auth'
import { api } from './request'

// 定义后端实际响应类型（解决类型断言问题）
interface BackendLoginResponse {
  success: boolean
  message: string
  access_token: string
  expires_in: number
  user: {
    username: string
    admin: boolean
  }
}

interface BackendSessionResponse {
  success: boolean
  message: string
  username: string
  email: string
  admin: boolean
}

/**
 * 认证API - 完全依赖统一架构拦截器
 * 移除所有手动错误处理，专注纯业务逻辑
 */
export const authApi = {
  /**
   * 用户登录 - 🔥 修复：返回后端真实的success状态
   */
  async login(loginData: LoginRequest): Promise<LoginResponse> {
    const formData = new URLSearchParams()
    formData.append('username', loginData.username)
    formData.append('password', loginData.password)

    // 后端登录接口返回原始数据，不是ApiResponse包装格式
    // 🔧 修复：API拦截器返回完整response，需要访问data属性
    const axiosResponse = await api.post<BackendLoginResponse>('/api/login', formData, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    })
    const response = axiosResponse.data

    // 🔥 修复：不再强制success为true，直接返回后端的真实状态
    if (!response.success) {
      return {
        success: false,
        message: response.message,
        data: {
          token: '',
          user: {
            id: '',
            username: '',
            email: '',
            role: '',
            createdAt: '',
            updatedAt: '',
          },
          expiresIn: 0,
        },
        code: 400, // 业务错误使用400状态码
      }
    }

    return {
      success: true,
      message: response.message,
      data: {
        token: response.access_token,
        user: {
          id: response.user.username,
          username: response.user.username,
          email: loginData.username,
          role: response.user.admin ? 'admin' : 'user',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        expiresIn: response.expires_in,
      },
      code: 200,
    }
  },

  /**
   * 获取用户信息 - 🔥 修复：返回后端真实的success状态
   */
  async getUserInfo(): Promise<UserInfoResponse> {
    // 🔧 修复：API拦截器返回完整response，需要访问data属性
    const axiosResponse = await api.get<BackendSessionResponse>('/api/session-status')
    const response = axiosResponse.data

    // 🔥 修复：不再强制success为true，直接返回后端的真实状态
    if (!response.success) {
      return {
        success: false,
        message: response.message,
        data: {
          id: '',
          username: '',
          email: '',
          role: '',
          createdAt: '',
          updatedAt: '',
        },
        code: 400, // 业务错误使用400状态码
      }
    }

    return {
      success: true,
      message: response.message,
      data: {
        id: response.username,
        username: response.username,
        email: response.email,
        role: response.admin ? 'admin' : 'user',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      code: 200,
    }
  },

  /**
   * 用户登出 - 纯业务逻辑，错误由拦截器处理
   */
  async logout(): Promise<ApiResponse> {
    // 🔧 修复：API拦截器返回完整response，需要访问data属性
    const axiosResponse = await api.post<{ success: boolean; message: string }>('/api/logout')
    const response = axiosResponse.data
    return {
      success: response.success,
      message: response.message,
      data: null,
      code: response.success ? 200 : 400, // 🔥 根据真实success状态设置code
    }
  },

  /**
   * 刷新令牌 - 纯业务逻辑，错误由拦截器处理
   */
  async refreshToken(): Promise<LoginResponse> {
    // 🔧 修复：API拦截器已返回response.data，无需类型断言
    const response = await api.post<{
      success: boolean
      message: string
      access_token: string
      expires_in: number
    }>('/api/refresh-token')

    // 🔥 修复：检查刷新token的真实success状态
    if (!response.success) {
      return {
        success: false,
        message: response.message,
        data: {
          token: '',
          user: {
            id: '',
            username: '',
            email: '',
            role: '',
            createdAt: '',
            updatedAt: '',
          },
          expiresIn: 0,
        },
        code: 400,
      }
    }

    // 🔥 修复：移除冗余的getUserInfo调用，直接返回token信息
    // 客户端会在需要时单独调用getUserInfo获取最新用户信息
    return {
      success: true,
      message: response.message,
      data: {
        token: response.access_token,
        user: {
          id: '',
          username: '',
          email: '',
          role: '',
          createdAt: '',
          updatedAt: '',
        },
        expiresIn: response.expires_in,
      },
      code: 200,
    }
  },
}

// 导出函数供现有代码使用
export const loginApi = authApi.login
export const getUserInfoApi = authApi.getUserInfo
export const logoutApi = authApi.logout
export const refreshTokenApi = authApi.refreshToken
