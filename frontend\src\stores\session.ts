import { api } from '@/api/request'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { useAuthStore } from './auth'

// 会话状态枚举
export const SessionStatus = {
  UNKNOWN: 'unknown' as const,
  VALID: 'valid' as const,
  EXPIRED: 'expired' as const,
  CHECKING: 'checking' as const,
} as const

export type SessionStatusType = (typeof SessionStatus)[keyof typeof SessionStatus]

export const useSessionStore = defineStore('session', () => {
  // 会话状态
  const sessionStatus = ref<SessionStatusType>(SessionStatus.UNKNOWN)

  // 会话监控定时器ID
  const monitoringTimer = ref<number | null>(null)

  // 是否启用会话监控
  const isMonitoringEnabled = ref(false)

  // 计算属性：会话是否有效
  const isSessionValid = computed(() => sessionStatus.value === SessionStatus.VALID)

  // 计算属性：会话是否过期
  const isSessionExpired = computed(() => sessionStatus.value === SessionStatus.EXPIRED)

  // 计算属性：是否正在检查会话
  const isCheckingSession = computed(() => sessionStatus.value === SessionStatus.CHECKING)

  // 会话状态
  const isSessionActive = ref(true)
  const sessionStartTime = ref<Date | null>(null)
  const lastActivityTime = ref<Date | null>(null)

  // 获取Cookie值，严格对应旧版Utils.getCookie
  function getCookie(name: string): string | null {
    const cookies = document.cookie.split(';')
    for (const cookie of cookies) {
      const [cookieName, value] = cookie.trim().split('=')
      if (cookieName === name) {
        return value
      }
    }
    return null
  }

  // 检查会话状态，使用统一的API封装层
  async function checkSession(): Promise<boolean> {
    // 如果已经在登录页面，跳过会话检查
    if (typeof window !== 'undefined' && window.location.pathname === '/login') {
      // 🔧 修复：移除调试日志
      return false
    }

    try {
      sessionStatus.value = SessionStatus.CHECKING
      // 🔧 修复：移除调试日志

      // 🔧 修复：使用统一的API封装层，符合架构设计原则
      const response = await api.get<Record<string, unknown>>('/api/session-status')

      // 处理ApiResponse结构
      const data = response.data ?? response

      if (data && data.success) {
        sessionStatus.value = SessionStatus.VALID
        // 🔧 修复：移除调试日志
        return true
      } else {
        sessionStatus.value = SessionStatus.EXPIRED
        // 🔧 修复：移除调试日志
        return false
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      const errorStatus =
        error && typeof error === 'object' && 'status' in error
          ? (error as { status: number }).status
          : null

      if (errorMessage === 'UNAUTHORIZED' || errorStatus === 401) {
        sessionStatus.value = SessionStatus.EXPIRED
        // 🔧 修复：移除调试日志

        // 🔧 修复：通过 authStore 统一清理认证状态（无需重复检查）
        const authStore = useAuthStore()
        authStore.clearAuth()
        // 🔧 修复：移除调试日志

        return false
      } else {
        console.warn('⚠️ 会话检查请求失败:', errorMessage)
        // 网络错误时不改变会话状态，避免误判
        sessionStatus.value = SessionStatus.UNKNOWN
        return false
      }
    }
  }

  // 检查本地是否有token
  function hasLocalToken(): boolean {
    return !!localStorage.getItem('auth_token')
  }

  // 自动检查token有效性（页面加载时调用）
  async function initializeSession(): Promise<void> {
    if (!hasLocalToken()) {
      sessionStatus.value = SessionStatus.EXPIRED
      // 🔧 修复：移除调试日志
      return
    }

    // 🔧 修复：移除调试日志
    const isValid = await checkSession()
    if (!isValid) {
      // 🔧 修复：移除调试日志
    } else {
      // 🔧 修复：移除调试日志
    }
  }

  // 启动会话监控，严格对应旧版SessionManager.startSessionMonitoring
  function startSessionMonitoring() {
    // 🔧 优化：避免重复启动监控
    if (isMonitoringEnabled.value) {
      return
    }

    sessionStartTime.value = new Date()
    lastActivityTime.value = new Date()
    isMonitoringEnabled.value = true

    // 监听用户活动
    const updateActivity = () => {
      lastActivityTime.value = new Date()
    }

    // 添加事件监听器
    document.addEventListener('click', updateActivity)
    document.addEventListener('keypress', updateActivity)
    document.addEventListener('mousemove', updateActivity)

    console.log('会话监控已启动')
  }

  // 停止会话监控
  function stopSessionMonitoring() {
    if (monitoringTimer.value) {
      clearInterval(monitoringTimer.value)
      monitoringTimer.value = null
      isMonitoringEnabled.value = false
      // 🔧 修复：移除调试日志
    }
  }

  // 标记会话过期（用于API调用发现401时）
  function markSessionExpired() {
    sessionStatus.value = SessionStatus.EXPIRED
    // 🔧 修复：移除调试日志
  }

  // 标记会话有效（用于登录成功后）
  function markSessionValid() {
    sessionStatus.value = SessionStatus.VALID
    // 🔧 修复：移除调试日志
  }

  // 重置会话状态
  function resetSessionStatus() {
    sessionStatus.value = SessionStatus.UNKNOWN
    // 🔧 修复：移除调试日志
  }

  // 处理会话过期后的清理工作
  function handleSessionExpired() {
    // 停止监控
    stopSessionMonitoring()

    // 清理相关状态（可以调用其他store的清理方法）
    // 🔧 修复：移除调试日志

    // 可以在这里触发全局事件通知其他组件
  }

  // 结束会话
  const endSession = () => {
    isSessionActive.value = false
    sessionStartTime.value = null
    lastActivityTime.value = null
    console.log('会话已结束')
  }

  return {
    // 状态
    sessionStatus,
    isMonitoringEnabled,

    // 计算属性
    isSessionValid,
    isSessionExpired,
    isCheckingSession,

    // 会话状态
    isSessionActive,
    sessionStartTime,
    lastActivityTime,

    // 方法
    getCookie,
    checkSession,
    hasLocalToken,
    initializeSession,
    startSessionMonitoring,
    stopSessionMonitoring,
    markSessionExpired,
    markSessionValid,
    resetSessionStatus,
    handleSessionExpired,
    endSession,
  }
})
