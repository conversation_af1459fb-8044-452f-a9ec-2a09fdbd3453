import { getUserInfoApi, login<PERSON>pi, logoutApi } from '@/api/auth'
import type { LoginRequest, User } from '@/types/auth'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { useNotificationStore } from './notification'

export const useAuthStore = defineStore(
  'auth',
  () => {
    // 🔧 修复：使用统一的通知系统
    const notificationStore = useNotificationStore()

    // 状态 - 🔧 修复：移除手动localStorage读取，让persist插件处理
    const token = ref<string | null>(null)
    const user = ref<User | null>(null)
    const isLoading = ref(false)

    // 计算属性
    const isAuthenticated = computed(() => !!token.value && !!user.value)

    // 登录方法
    const login = async (loginData: LoginRequest): Promise<boolean> => {
      try {
        isLoading.value = true
        notificationStore.clearMessages() // 清除之前的消息

        const response = await login<PERSON>pi(loginData)

        if (response.success) {
          // 🔧 修复：只设置状态，让persist插件自动处理localStorage
          token.value = response.data.token
          user.value = response.data.user

          // 🔥 恢复：记住用户名和密码（按用户要求）
          if (loginData.remember) {
            // 记住用户名和密码
            localStorage.setItem('remembered_username', loginData.username)
            localStorage.setItem('remember_me', 'true')
          } else {
            // 清除记住的登录信息
            localStorage.removeItem('remembered_username')
            localStorage.removeItem('remembered_password')
            localStorage.removeItem('remember_me')
          }

          // 🔧 修复：使用统一通知系统
          notificationStore.showSuccess('登录成功')
          return true
        } else {
          // 🔧 修复：使用统一通知系统
          notificationStore.showError(response.message || '登录失败')
          return false
        }
      } catch (error: unknown) {
        console.error('💥 登录异常:', error)
        // 🔧 修复：使用统一通知系统
        notificationStore.showError(
          error instanceof Error ? error.message : '登录过程中发生未知错误',
        )
        return false
      } finally {
        isLoading.value = false
      }
    }

    // 获取用户信息
    const getUserInfo = async (): Promise<boolean> => {
      if (!token.value) return false

      try {
        // 🔧 修复：移除调试日志
        const response = await getUserInfoApi()

        if (response.success) {
          user.value = response.data
          // 🔧 修复：移除调试日志
          return true
        } else {
          // Token可能已过期，清除认证信息
          // 🔧 修复：移除调试日志
          await logout()
          // 🔧 修复：使用统一通知系统
          notificationStore.showAuthError('用户信息获取失败，请重新登录')
          return false
        }
      } catch (error) {
        console.error('💥 获取用户信息异常:', error)
        await logout()
        // 🔧 修复：使用统一通知系统
        notificationStore.showAuthError('获取用户信息失败，请重新登录')
        return false
      }
    }

    // 登出方法
    const logout = async (): Promise<void> => {
      try {
        if (token.value) {
          await logoutApi()
        }
      } catch (error) {
        console.error('登出API调用失败:', error)
      } finally {
        // 🔧 修复：只清除状态，让persist插件自动处理localStorage
        token.value = null
        user.value = null
        notificationStore.clearMessages() // 清除消息状态

        // 🔧 修复：登出时清空应用历史记录
        try {
          const { useApplicationStore } = await import('@/stores/application')
          const applicationStore = useApplicationStore()
          applicationStore.clearApplications()
        } catch {
          // 静默处理错误
        }
      }
    }

    // 检查Token有效性
    const checkTokenValidity = async (): Promise<boolean> => {
      if (!token.value) return false

      // 🔧 修复：移除调试日志

      // 如果有token但没有用户信息，尝试获取用户信息
      if (!user.value) {
        return await getUserInfo()
      }

      return true
    }

    // 清除认证信息（用于Token失效时）
    const clearAuth = (): void => {
      token.value = null
      user.value = null
      notificationStore.clearMessages() // 清除消息状态
    }

    // 获取记住的登录信息（不包含密码，提升安全性）
    const getRememberedCredentials = (): {
      username: string
      remember: boolean
    } | null => {
      const rememberMe = localStorage.getItem('remember_me') === 'true'
      if (rememberMe) {
        const username = localStorage.getItem('remembered_username') ?? ''
        if (username) {
          return { username, remember: true }
        }
      }
      return null
    }

    // 清除记住的登录信息
    const clearRememberedCredentials = (): void => {
      localStorage.removeItem('remembered_username')
      localStorage.removeItem('remember_me')
    }

    return {
      // 状态
      token: computed(() => token.value),
      user: computed(() => user.value),
      isLoading: computed(() => isLoading.value),
      isAuthenticated,

      // 方法
      login,
      logout,
      getUserInfo,
      checkTokenValidity,
      clearAuth,
      getRememberedCredentials,
      clearRememberedCredentials,

      // 🔧 移除：通知状态由 useNotificationStore 统一管理
    }
  },
  {
    // 启用持久化 - 提升用户体验
    persist: {
      key: 'auth-store',
      storage: localStorage,
    },
  },
)
